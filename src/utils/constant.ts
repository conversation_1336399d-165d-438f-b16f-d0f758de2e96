const QUERY_OPTIONS = {
  SELECT: "$select",
  FILTER: "$filter",
  ORDERBY: "$orderby",
  EXPAND: "$expand",
  SKIP: "$skip",
  TOP: "$top",
  FORMAT: "$format",
  INLINECOUNT: "$inlinecount",
  APPLY: "$apply",
  SUPPORTED: "$supported",
  COUNT: "$count",
  SKIP_TOKEN: "$skiptoken",
  DELTA_TOKEN: "$deltatoken",
};

const OPERATORS = {
  LOGICAL: {
    AND: "and",
    OR: "or",
    NOT: "not",
  },
  COMPARISON: {
    EQUAL: "eq",
    NOT_EQUAL: "ne",
    GREATER_THAN: "gt",
    GREATER_THAN_OR_EQUAL: "ge",
    LESS_THAN: "lt",
    LESS_THAN_OR_EQUAL: "le",
    HAS: "has", // not implemented and need to check $filter=PropertyName has EnumType'FlagValue'
  },
  IN: "in",
  GROUPING: {
    OPEN: "(",
    CLOSE: ")",
  },
  STRING_FUNCTIONS: {
    TOLOWER: "tolower",
    TOUPPER: "toupper",
    TRIM: "trim",
    SUBSTRING: "substring",
    CONTAINS: "contains",
    ENDSWITH: "endswith",
    STARTSWITH: "startswith",
    INDEX_OF: "indexof",
    LENGTH: "length",
    REPLACE: "replace", // not implemented
    CONCAT: "concat", // not implemented
  },
  DATE_FUNCTIONS: {
    DATE: "date", // not implemented
    TIME: "time", // not implemented
    DAY: "day", // not implemented
    MONTH: "month", // not implemented
    YEAR: "year", // not implemented
    HOUR: "hour", // not implemented
    MINUTE: "minute", // not implemented
    SECOND: "second", // not implemented,
    FRACTIONAL_SECONDS: "fractionalseconds", // not implemented
  },
  ARITHMETIC: {
    ADD: "add", // not implemented /products?$filter=Price add 10 gt 100
    SUB: "sub", // not implemented
    MUL: "mul", // not implemented /products?$filter=Price mul 2 lt 500
    DIV: "div", // not implemented
    MOD: "mod", // not implemented
  },
  MATH_FUNCTIONS: {
    ROUND: "round", // not implemented
    FLOOR: "floor", // not implemented Orders?$filter=floor(Freight) eq 33 or Orders?$filter=ceiling(Freight) eq 33d
    CEILING: "ceiling", // not implemented
  },
  TYPE_FUNCTIONS: {
    CAST: "cast", // not implemented
    ISOF: "isof", // not implemented /Orders?$filter=isof(ShipCountry, 'Edm.String')
    ANY: "any", // not implemented
    ALL: "all", // not implemented
  },
  GEO: {
    DISTANCE: "geo.distance", // not implemented
    INTERSECTS: "geo.intersects", // not implemented
  },
  COLLECTION: {
    IN: "in",
    ANY: "any", // not implemented
    ALL: "all", // not implemented
  },
};

const ODATA_SETTING = {
  EXPAND_DEPTH: 4,
  SELECT_DEPTH: 4,
};
export { QUERY_OPTIONS, OPERATORS, ODATA_SETTING };
