import { SequelizeModelController } from "../adaptors/sequelizer";
import { IControllerConfig, IMethod, IQuery } from "../types";
import { DataSource } from "./dataSource";
import { EntitySchema } from "./entitySchema";
import { QueryParser } from "./query-parser";

export class ODataControler {
  public config: IControllerConfig;

  constructor(config: IControllerConfig) {
    this.config = config;
  }

  public get(query: IQuery): IQuery {
    console.log("-----query--->", query);
    return query;
  }

  public post(query: IQuery): IQuery {
    return query;
  }

  public delete(query: IQuery): IQuery {
    return query;
  }

  public put(query: IQuery): IQuery {
    return query;
  }

  public getAllowedMethod(): IMethod[] {
    if (this.config.allowedMethod) {
      return this.config.allowedMethod;
    } else {
      return ["get"];
    }
  }

  public getEndpoint(): string {
    if (this.config.endpoint) {
      return this.config.endpoint;
    } else {
      return this.config.model.entityName;
    }
  }

  public queryble(query: QueryParser) {
    this.getSequilzeModels(query);
  }

  private getSequilzeModels(query: QueryParser) {
    const entities: EntitySchema[] = query.getDataSource().entities;
    const sequlizerModels = {};
    entities.map((entity: EntitySchema) => {
      const sequlizerModel: SequelizeModelController =
        entity.getSequelizerEntitySchema();
      sequlizerModel.tableName;
    });
  }
}
