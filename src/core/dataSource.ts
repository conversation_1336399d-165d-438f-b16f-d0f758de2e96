import { IAttibutes, IDbConfig, IEntitySchemaOptions } from "../types";
import { EntitySchema } from "./entitySchema";
import { SequelizerAdaptor } from "../adaptors";

type TypedAttributes<T extends IAttibutes> = {
  readonly [K in keyof T]: T[K];
};

export interface TypedModel<T extends IAttibutes> extends EntitySchema {
  attributes: TypedAttributes<T>;
}

export class DataSource {
  private dbConfig = {};
  private sequelizerAdaptor: SequelizerAdaptor;
  public entities: EntitySchema[] = [];

  constructor(dbConfig: IDbConfig) {
    this.dbConfig = dbConfig;
    this.sequelizerAdaptor = new SequelizerAdaptor(dbConfig);
  }

  public defineEntity<T extends IAttibutes>(
    entityName: string,
    attributes: T,
    options?: IEntitySchemaOptions
  ): TypedModel<T> {
    const sequelizerModel = this.sequelizerAdaptor.define(
      entityName,
      attributes,
      options
    );
    sequelizerModel.belongsTo(sequelizerModel, {});
    const model = new EntitySchema(
      entityName,
      attributes,
      sequelizerModel,
      options
    );
    this.entities.push(model);
    return model as TypedModel<T>;
  }

  public getDbConfig() {
    return this.dbConfig;
  }
}
