import {
  IRawSearchParams,
  SelectField,
  OrderByClause,
  ExpandClause,
  FilterClause,
  IQueryParseOptions,
  IParsedQuery,
} from "../../types";
import { DataSource } from "../dataSource";
import { parseExpand } from "./parseExpand";
import { parseFilter } from "./parseFilter";
import { parseOrderBy } from "./parseOrderBy";
import { parseSelect } from "./parseSelect";

class QueryParser {
  private dataSource: DataSource;
  private options: IQueryParseOptions;
  public queryString: string;
  public baseTableName!: string;

  public select: SelectField[] = [];
  public orderBy: OrderByClause[] = [];
  public expand: ExpandClause[] = [];
  public filter?: FilterClause;
  public skip: number = 0;
  public top: number = 0;

  constructor(
    queryString: string,
    dataSource: DataSource,
    options?: IQueryParseOptions
  ) {
    this.queryString = queryString;
    this.dataSource = dataSource;
    this.options = options || {};
    this.parse(queryString);
  }

  private parse(queryString: string) {
    const urlParts = queryString.split("?");
    const baseTableName = urlParts[0].startsWith("/")
      ? urlParts[0].split("/")[1]
      : urlParts[0];
    const searchParams = urlParts[1];
    const rawSearchParams: IRawSearchParams =
      this.getSearchParams(searchParams);
    this.baseTableName = baseTableName;
    this.processRawSearchParams(rawSearchParams);
  }

  private getSearchParams(searchParams: string) {
    const params = new URLSearchParams(searchParams);
    const paramObject: IRawSearchParams = {
      select: "*",
      filter: "",
      orderby: "",
      expand: "",
      skip: 0,
      top: 0,
    };
    params.forEach((value, key) => {
      switch (key.toLowerCase()) {
        case "$select":
          paramObject["select"] = value;
          break;
        case "$filter":
          paramObject["filter"] = value;
          break;
        case "$orderby":
          paramObject["orderby"] = value;
          break;
        case "$expand":
          paramObject["expand"] = value;
          break;
        case "$skip":
          paramObject["skip"] = parseInt(value, 10);
          break;
        case "$top":
          paramObject["top"] = parseInt(value, 10);
          break;
      }
    });
    return paramObject;
  }

  private processRawSearchParams(rawSearchParams: IRawSearchParams) {
    this.select = parseSelect(rawSearchParams.select, this.baseTableName);
    this.orderBy = parseOrderBy(rawSearchParams.orderby, this.baseTableName);
    this.expand = parseExpand(rawSearchParams.expand);
    this.filter = parseFilter(rawSearchParams.filter, this.baseTableName);
    this.top = rawSearchParams.top || this.options.defaultTop || 0;
    this.skip = rawSearchParams.skip || this.options.defaultSkip || 0;
  }

  public setSelect(select: SelectField[]) {
    this.select = select;
  }

  public setOrderBy(orderBy: OrderByClause[]) {
    this.orderBy = orderBy;
  }

  public setExpandF(expand: ExpandClause[]) {
    this.expand = expand;
  }

  public setFilter(filter: FilterClause) {
    this.filter = filter;
  }

  public setTop(top: number) {
    this.top = top;
  }

  public setSkip(skip: number) {
    this.skip = skip;
  }

  public getParams(): IParsedQuery {
    return {
      table: this.baseTableName,
      select: this.select,
      orderBy: this.orderBy,
      expand: this.expand,
      filter: this.filter,
      top: this.top,
      skip: this.skip,
    };
  }

  public getDataSource() {
    return this.dataSource;
  }
}

export { QueryParser };
