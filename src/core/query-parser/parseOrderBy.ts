import { OrderByClause } from "../../types";

const parseOrderBy = (
  orderByClause: string,
  tableName: string
): OrderByClause[] => {
  if (orderByClause.trim() === "") {
    return [];
  }
  const orderItems = orderByClause.split(",");

  const orderByFeilds: OrderByClause[] = orderItems.map((item) => {
    const parts = item.trim().split(/\s+/);
    return {
      field: parts[0],
      table: tableName,
      direction: parts[1]?.toLowerCase() === "desc" ? "desc" : "asc",
    };
  });
  return orderByFeilds;
};

export { parseOrderBy };
