import { SelectField } from "../../types";

const parseSelect = (
  selectClause: string,
  tableName: string
): SelectField[] => {
  const fields: string[] = selectClause
    .split(",")
    .map((field) => field.trim())
    .filter((field) => {
      return field !== null && field.length > 0 && field !== "*";
    });
  const formattedFeilds: SelectField[] = fields.map((field) => {
    return {
      table: tableName,
      field,
    };
  });

  return formattedFeilds;
};

export { parseSelect };
