import { FilterClause } from "../../types";
import { OPERATORS } from "../../utils/constant";

const parseFilter = (
  filterClause: string,
  tableName: string
): FilterClause | undefined => {
  if (filterClause.trim() === "") {
    return undefined;
  }
  const result: FilterClause = parseODataExpression(filterClause, tableName);
  return result;
};

function parseODataExpression(input: string, tableName: string): FilterClause {
  const tokens = tokenizeOData(input);
  const result = parseTokens(tokens, tableName);
  Array.isArray(result) ? result : [result];
  return result;
}

function tokenizeOData(input: string): string[] {
  const tokens: string[] = [];
  let i = 0;

  while (i < input.length) {
    // Skip whitespace
    if (input[i] === " ") {
      i++;
      continue;
    }

    // Handle string literals
    if (input[i] === "'" || input[i] === '"') {
      const quote = input[i];
      let str = quote;
      i++;
      while (i < input.length && input[i] !== quote) {
        str += input[i];
        i++;
      }
      if (i < input.length) {
        str += input[i]; // closing quote
        i++;
      }
      tokens.push(str);
      continue;
    }

    // Handle parentheses and commas
    if (input[i] === "(" || input[i] === ")" || input[i] === ",") {
      tokens.push(input[i]);
      i++;
      continue;
    }

    // Handle words/identifiers/operators
    let word = "";
    while (
      i < input.length &&
      input[i] !== " " &&
      input[i] !== "(" &&
      input[i] !== ")" &&
      input[i] !== "," &&
      input[i] !== "'" &&
      input[i] !== '"'
    ) {
      word += input[i];
      i++;
    }

    if (word) {
      tokens.push(word);
    }
  }

  return tokens;
}

function parseTokens(tokens: string[], tableName: string): FilterClause {
  let index = 0;

  function parseLogicalExpression(): FilterClause {
    let left = parseCondition();

    const conditions = [left];
    let logicalOp: (typeof OPERATORS.LOGICAL)[keyof typeof OPERATORS.LOGICAL] =
      OPERATORS.LOGICAL.AND; // default

    while (
      index < tokens.length &&
      (tokens[index]?.toLowerCase() === OPERATORS.LOGICAL.AND ||
        tokens[index]?.toLowerCase() === OPERATORS.LOGICAL.OR)
    ) {
      logicalOp = tokens[index].toLowerCase();
      index++; // consume logical operator
      const right = parseCondition();
      conditions.push(right);
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    return {
      table: tableName,
      logicalOperator: logicalOp as "and" | "or" | "not",
      conditions: conditions,
    };
  }

  function parseCondition(): any {
    // Handle NOT operator
    if (tokens[index]?.toLowerCase() === OPERATORS.LOGICAL.NOT) {
      index++; // consume 'not'
      const condition = parseCondition();
      return {
        operator: OPERATORS.LOGICAL.NOT,
        condition: condition,
      };
    }

    // Handle parentheses
    if (tokens[index] === "(") {
      index++; // consume '('
      const result = parseLogicalExpression();
      if (tokens[index] === ")") {
        index++; // consume ')'
      }
      return result;
    }

    // Check if this is a function call
    if (index + 1 < tokens.length && tokens[index + 1] === "(") {
      return parseFunctionCall();
    }

    // Regular field operator value
    const simpleCondition = parseSimpleCondition();
    return {
      table: tableName,
      logicalOperator: OPERATORS.LOGICAL.AND,
      conditions: [simpleCondition],
    };
  }

  function parseFunctionCall(): any {
    const functionName = tokens[index].toLowerCase();
    index++; // consume function name
    index++; // consume '('

    const args = [];

    // Parse function arguments
    while (index < tokens.length && tokens[index] !== ")") {
      if (tokens[index] === ",") {
        index++; // consume comma
        continue;
      }

      // Check if argument is another function call
      if (index + 1 < tokens.length && tokens[index + 1] === "(") {
        args.push(parseFunctionCall());
      } else {
        args.push(tokens[index]);
        index++;
      }
    }

    if (tokens[index] === ")") {
      index++; // consume closing ')'
    }

    // Handle different function types
    switch (functionName) {
      case OPERATORS.STRING_FUNCTIONS.CONTAINS:
      case OPERATORS.STRING_FUNCTIONS.STARTSWITH:
      case OPERATORS.STRING_FUNCTIONS.ENDSWITH: {
        let field = args[0];
        let fieldStringOperator = null;

        // Check if first arg is a function result
        if (typeof args[0] === "object" && args[0].functionName) {
          field = args[0].field;
          fieldStringOperator = args[0].functionName;
        }

        const value = parseStringValue(args[1]);

        // Check for optional 'eq true/false'
        if (index < tokens.length && tokens[index]?.toLowerCase() === "eq") {
          index++; // consume 'eq'
          index++; // consume boolean
        }

        const condition: any = {
          field,
          operator: functionName,
          value,
        };

        if (fieldStringOperator) {
          condition.fieldStringOperator = fieldStringOperator;
        }

        return condition;
      }

      case OPERATORS.STRING_FUNCTIONS.LENGTH: {
        const field = args[0];

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error("Expected comparison operator after length function");
        }

        const operator = tokens[index];
        index++;
        const value = parseValue(tokens[index]);
        index++;

        return {
          field,
          operator,
          value,
          fieldStringOperator: OPERATORS.STRING_FUNCTIONS.LENGTH,
        };
      }

      case OPERATORS.STRING_FUNCTIONS.INDEX_OF: {
        const field = args[0];
        const searchValue = parseStringValue(args[1]);

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error(
            "Expected comparison operator after indexof function"
          );
        }

        const operator = tokens[index];
        index++;
        const value = parseValue(tokens[index]);
        index++;

        return {
          field,
          operator,
          value,
          fieldStringOperator: OPERATORS.STRING_FUNCTIONS.INDEX_OF,
          stringSearchValue: searchValue,
        };
      }

      case OPERATORS.STRING_FUNCTIONS.TOLOWER:
      case OPERATORS.STRING_FUNCTIONS.TOUPPER: {
        const field = args[0];

        // This could be used standalone or as part of another function
        if (
          index < tokens.length &&
          (tokens[index]?.toLowerCase() === OPERATORS.COMPARISON.EQUAL ||
            tokens[index]?.toLowerCase() === OPERATORS.COMPARISON.NOT_EQUAL ||
            tokens[index]?.toLowerCase() ===
              OPERATORS.COMPARISON.GREATER_THAN ||
            tokens[index]?.toLowerCase() ===
              OPERATORS.COMPARISON.GREATER_THAN_OR_EQUAL ||
            tokens[index]?.toLowerCase() === OPERATORS.COMPARISON.LESS_THAN ||
            tokens[index]?.toLowerCase() ===
              OPERATORS.COMPARISON.LESS_THAN_OR_EQUAL)
        ) {
          const operator = tokens[index];
          index++;
          const value = parseStringValue(tokens[index]);
          index++;

          return {
            field,
            operator,
            value,
            fieldStringOperator: functionName,
          };
        } else {
          // Return as function result for nested use
          return {
            functionName,
            field,
          };
        }
      }

      case OPERATORS.STRING_FUNCTIONS.SUBSTRING: {
        const field = args[0];
        const startIdx = parseValue(args[1]);
        const length = args.length > 2 ? parseValue(args[2]) : null;

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error(
            "Expected comparison operator after substring function"
          );
        }

        const operator = tokens[index];
        index++;
        const value = parseStringValue(tokens[index]);
        index++;

        const condition: any = {
          field,
          operator,
          value,
          fieldStringOperator: OPERATORS.STRING_FUNCTIONS.SUBSTRING,
          substringStart: startIdx,
        };

        if (length !== null) {
          condition.substringLength = length;
        }

        return condition;
      }

      default:
        throw new Error(`Unknown function: ${functionName}`);
    }
  }

  function parseSimpleCondition(): any {
    const field = tokens[index];
    index++;
    const operator = tokens[index];
    index++;
    // Handle 'in' operator specially - it expects an array of values
    if (operator.toLowerCase() === OPERATORS.IN) {
      // Expect opening parenthesis
      if (tokens[index] !== "(") {
        throw new Error('Expected "(" after "in" operator');
      }
      index++; // consume '('
      const values = [];
      while (index < tokens.length && tokens[index] !== ")") {
        if (tokens[index] === ",") {
          index++; // consume comma

          continue;
        }
        values.push(parseValue(tokens[index]));
        index++;
      }
      if (tokens[index] === ")") {
        index++; // consume closing ')'
      }
      return {
        field,
        operator: OPERATORS.IN,
        value: values,
      };
    }
    // Regular operator with single value
    const value = parseValue(tokens[index]);
    index++;

    return { field, operator, value };
  }

  function parseValue(token: string): any {
    if (!token) return token;

    if (token.startsWith("'") && token.endsWith("'")) {
      return token.slice(1, -1);
    }
    if (token.startsWith('"') && token.endsWith('"')) {
      return token.slice(1, -1);
    }

    const num = Number(token);
    if (!isNaN(num)) {
      return num;
    }

    if (token.toLowerCase() === "true") return true;
    if (token.toLowerCase() === "false") return false;
    if (token.toLowerCase() === "null") return null;

    return token;
  }

  function parseStringValue(token: string): any {
    return parseValue(token);
  }

  return parseLogicalExpression();
}

export { parseFilter };
