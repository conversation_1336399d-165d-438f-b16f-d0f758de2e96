import { SequelizeModelController } from "../adaptors/sequelizer";
import { IEntitySchemaOptions, IAttibutes } from "../types";

export class EntitySchema {
  public readonly entityName: string;
  public readonly attributes: IAttibutes;
  private readonly options: IEntitySchemaOptions;
  private sequelizerModel: SequelizeModelController;

  constructor(
    entityName: string,
    attributes: IAttibutes,
    sequelizerModel: SequelizeModelController,
    options?: IEntitySchemaOptions
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.sequelizerModel = sequelizerModel;
    this.options = options || {};
  }

  public getEntityName() {
    return this.entityName;
  }

  public getAttributes() {
    return this.attributes;
  }

  public getOptions() {
    return this.options;
  }

  public setSequelizerModel(model: SequelizeModelController) {
    return (this.sequelizerModel = model);
  }

  public getSequelizerEntitySchema(): SequelizeModelController {
    return this.sequelizerModel;
  }

  /**
   * Get list of available field names
   */
  public getFieldNames(): string[] {
    return Object.keys(this.attributes);
  }

  /**
   * Check if a field exists
   */
  public hasField(fieldName: string): boolean {
    return fieldName in this.attributes;
  }

  /**
   * Get field metadata safely
   */
  public getField(fieldName: string) {
    if (!this.hasField(fieldName)) {
      const availableFields = this.getFieldNames().join(", ");
      throw new Error(
        `Field '${fieldName}' does not exist. Available fields: ${availableFields}`
      );
    }
    return this.attributes[fieldName];
  }
}
