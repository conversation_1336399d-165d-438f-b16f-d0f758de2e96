import { IExpressRouterConfig, IMethod } from "../../types";
import { Router, Express } from "express";
import { ODataControler, QueryParser, DataSource } from "../";

export class ExpressRouter {
  private config: IExpressRouterConfig;
  private app: Express;
  private controllers: ODataControler[];
  private dataSource: DataSource;

  constructor(app: Express, config: IExpressRouterConfig) {
    this.config = config;
    this.app = app;
    this.controllers = config.controllers;
    this.dataSource = config.dataSource;
    this.setUpRouters();
  }

  private setUpRouters() {
    this.controllers.forEach((controller) => {
      const router = Router();
      const allowedMethods: IMethod[] = controller.getAllowedMethod();
      const endpoint = controller.getEndpoint();
      allowedMethods.forEach((method: IMethod) => {
        if (method === "get") {
          router.get("/", (req, res) => {
            const queryParser = new QueryParser(
              `${req.baseUrl}${req.url}`,
              this.dataSource
            );
            const query = controller.get(queryParser);
            console.log(
              "-----query--->",
              JSON.stringify(queryParser.getParams())
            );
            res.send(query);
          });
          return;
        }
        if (method === "post") {
          router.post("/", (req, res) => {
            const query = controller.post(req.query);
            res.send(query);
          });
          return;
        }
        if (method === "put") {
          router.put("/", (req, res) => {
            const query = controller.put(req.query);
            res.send(query);
          });
          return;
        }
        if (method === "delete") {
          router.delete("/", (req, res) => {
            const query = controller.delete(req.query);
            res.send(query);
          });
          return;
        }
      });
      const routePath = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      this.app.use(routePath, router);
    });
  }

  public getApp() {
    return this.app;
  }

  public getConfig() {
    return this.config;
  }
}
