import {
  DataTypes as SequelizeDataTypes,
  DataType as SequelizeDataTypeInterface,
} from "sequelize";
import { DataSource, EntitySchema, ODataControler } from "../core";

export * from "./queryParser.types";

const DataTypes = SequelizeDataTypes;
type IDataType = SequelizeDataTypeInterface;

interface IColumn {
  dataType: IDataType;
  columnName?: string;
  isPrimaryKey?: boolean;
  isNullable?: boolean;
  isUnique?: boolean;
  isAutoIncrement?: boolean;
  onDelete?: "CASCADE";
  onUpdate?: "CASCADE";
  defaultValue?: string | number | Date | boolean;
}

type IAttibutes = Record<string, IColumn>;

interface IEntitySchemaOptions {}

type Dialect =
  | "mysql"
  | "postgres"
  | "sqlite"
  | "mariadb"
  | "mssql"
  | "db2"
  | "snowflake"
  | "oracle";

interface PoolOptions {
  /**
   * Maximum number of connections in pool. Default is 5
   */
  max?: number;

  /**
   * Minimum number of connections in pool. Default is 0
   */
  min?: number;

  /**
   * The maximum time, in milliseconds, that a connection can be idle before being released
   */
  idle?: number;

  /**
   * The maximum time, in milliseconds, that pool will try to get connection before throwing error
   */
  acquire?: number;

  /**
   * The time interval, in milliseconds, after which sequelize-pool will remove idle connections.
   */
  evict?: number;

  /**
   * The number of times to use a connection before closing and replacing it.  Default is Infinity
   */
  maxUses?: number;
}

interface IDbConfig {
  database: string;
  username: string;
  password: string;
  host: string;
  dialect: Dialect;
  port: number;
  pool: PoolOptions;
  schema: string;
  ssl?: boolean;
  entities?: EntitySchema[];
}

type IMethod = "get" | "post" | "put" | "delete";

interface IControllerConfig {
  endpoint?: string;
  allowedMethod?: IMethod[];
  model: EntitySchema;
}

interface IQuery {}

interface IExpressRouterConfig {
  controllers: ODataControler[];
  dataSource: DataSource;
}

export {
  IColumn,
  IAttibutes,
  DataTypes,
  IDataType,
  IEntitySchemaOptions,
  IDbConfig,
  Dialect,
  PoolOptions,
  IControllerConfig,
  IMethod,
  IQuery,
  IExpressRouterConfig,
};
