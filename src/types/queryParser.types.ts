interface IQueryParseOptions {
  defaultTop?: number;
  defaultSkip?: number;
}
interface IRawSearchParams {
  select: string;
  filter: string;
  orderby: string;
  expand: string;
  skip: number;
  top: number;
}
interface IParsedQuery {
  select: SelectField[];
  table: string;
  filter?: FilterClause;
  orderBy: OrderByClause[];
  skip: number;
  top: number;
  expand: ExpandClause[];
}

interface SelectField {
  field: string;
  table: string; // Table alias if from joined table
  alias?: string; // Column alias
}

interface OrderByClause {
  field: string;
  table: string;
  direction: "asc" | "desc";
}

interface ExpandClause {
  tableName: string;
  select?: SelectField[];
  filter?: FilterClause;
  orderby?: OrderByClause[];
  skip?: number;
  top?: number;
  expand?: ExpandClause[];
  type: "inner" | "left" | "right";
}

interface FilterCondition {
  field: string;
  table?: string;
  operator: string;
  value: any;
  fieldStringOperator?: string;
  stringSearchValue?: string;
  substringStart?: number;
  substringLength?: number;
}

interface FilterClause {
  table: string;
  logicalOperator: "and" | "or" | "not";
  conditions: (FilterCondition | FilterClause)[];
}

export {
  IQueryParseOptions,
  IRawSearchParams,
  IParsedQuery,
  SelectField,
  FilterClause,
  OrderByClause,
  ExpandClause,
};
