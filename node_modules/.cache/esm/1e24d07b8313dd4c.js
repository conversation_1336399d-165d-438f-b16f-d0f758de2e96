/*
 * wallaby-core - v1.0.1851
 * https://wallabyjs.com
 * Copyright (c) 2014-2025 Wallaby.js - All Rights Reserved.
 *
 * This source code file is a part of wallaby-core and is a proprietary (closed source) software.

 * IMPORTANT:
 * Wallaby.js is a tool made by software developers for software developers with passion and love for what we do.
 * Pirating the tool is not only illegal and just morally wrong,
 * it is also unfair to other fellow programmers who are using it legally,
 * and very harmful for the tool and its future.
 */
var path=require("path");const url=require("url"),fs=require("fs"),semver=require("semver");var Module=require("module"),tracer=global.$_$tracer,utils=tracer._utils,file,entryModule,quokkaSettings={},quokkaSettingsDirPath,quokkaSettingsDirNodeModulesPath,quokkaTempDirNodeModulesPath,beforeExitHandlers=[],startTime,serverPath=path.dirname(process.mainModule.filename);const isBun=!!process.versions.bun;let quokkaFileExists=!0;const isWindows="win32"===process.platform,viteNodeContext={},localProjectRoot=path.dirname(global.wallaby._localNodeModules);let registered=!1,registerReadyPromise=void 0,registerReady=()=>{};function patchConsoleError(){const e=global.console.error;global.console.error=function(){if((arguments.length&&Buffer.isBuffer(arguments[0])&&(arguments[0]=arguments[0].toString("utf8")),arguments.length)&&0===arguments[0].toString("utf8").split("\n").filter(e=>-1===e.indexOf("ExperimentalWarning:")&&-1===e.indexOf("node --trace-warnings")).join("\n").length)return;e.call(global.console,...arguments)}}async function runInViteNode(viteNodeServer){await viteNodeServer.reset({path:viteNodeContext.file.path,content:viteNodeContext.file.content});const{ViteNodeRunner}=await eval(_831‍.c("import(url.pathToFileURL(quokkaSettings.vite.viteNodeClientPath))")),viteNodeRunner=new ViteNodeRunner({root:viteNodeServer.config.root,base:viteNodeServer.config.base,async resolveId(e,t){return viteNodeServer.resolveId({id:e,importer:t})},async fetchModule(t){try{var e=await viteNodeServer.fetchModule({moduleId:t}),r=t.replace(/\\/g,"/").replace(/^\/@fs\//,isWindows?"":"/").replace(/^file:\//,"/").replace(/^node:/,"").replace(/^\/+/,"/"),o=viteNodeContext.instrumentedFiles[r];return o&&global.$_$coverage&&(o.lineMap=utils.updateFileMap({},[e.map]),tracer.sendTransformedFile(o)),delete e.map,e}catch(e){throw"string"==typeof e?new Error(e):e.frame?new Error(`Error while loading module ${e.plugin?"(plugin: "+e.plugin+")":""}: ${t}:
`+e.frame):e}}});await viteNodeRunner.executeFile(viteNodeContext.file.path)}async function createViteNodeServer(){const e=require("child_process").fork,t=path.join(__dirname,"vite-server.js");return new Promise((r,o)=>{const n=e(t,[],{stdio:["ignore","ignore","ignore","ipc"]}),l=(n.unref(),n.channel.unref(),{transform:async({transformed:t,fileName:e})=>{if(!global.$_$coverage)return t;t.map&&t.map.sources&&(t.map.sources=t.map.sources.map(e=>e||viteNodeContext.file.path));let r;t.map&&(viteNodeContext.file.changeStart||viteNodeContext.file.logMarkers&&viteNodeContext.file.logMarkers.length||viteNodeContext.file.extractedComments&&!viteNodeContext.file.extractedComments.isCoverageIgnored||viteNodeContext.file.test)&&(r=new utils.SourceMapConsumer(t.map));var o=utils.mapTextPosition(viteNodeContext.file.changeStart&&viteNodeContext.file.changePosition,r),i=viteNodeContext.file.logMarkers||[];let s;var a=viteNodeContext.file.path;try{s=utils.instrument(t.code,{file:viteNodeContext.file.id,test:viteNodeContext.file.test,fileName:path.basename(a),testFileChangeStart:o,hints:global.$_$tracer._hints,recordValues:global.$_$tracer._autoConsoleLog,captureConsoleLog:!0,recordMatchSnapshotRanges:!0,preserveComments:global.$_$tracer._preserveComments,logMarkers:i.map(e=>({originalRange:e.range,range:utils.mapOriginalRangeToTransformed(r,e.range),changeId:e.id,traceId:e.traceId,expanded:e.expanded,new:e.new,exp:e.exp,action:e.action,logpoint:e.logpoint,inlineLogpoint:e.inlineLogpoint})),extractedComments:utils.remapComments(viteNodeContext.file.extractedComments,t.code,r),sequenceExpressionToDiff:!0,vue:"vue"===quokkaSettings.vite.fileType,svelte:"svelte4"===quokkaSettings.vite.fileType?"4":"svelte5"===quokkaSettings.vite.fileType&&"5",snaps:{enabled:quokkaSettings.snaps,onlyMode:quokkaSettings.snapsOnlyMode,asyncWrap:!1},recordFunctions:!0})}catch(e){throw utils.formatInstrumentationError(e,t.code,a,localProjectRoot)}let n;if(quokkaSettings.snapsOnlyMode){if(!s.snaps||!s.snaps.length)throw new Error(utils.snapsNotFoundError);s.snaps.find(e=>!e.empty)||(n=new Error(utils.snapsAreEmptyError)),!global.$_$snapsAutoRun&&s.snaps&&s.snaps.length&&(n=new Error(utils.snapsFoundButNoAutoRunError))}tracer._autoExpandAllLogs=quokkaSettings.snaps&&!!(s.snaps||[]).find(e=>e.output),s.liveCommentLines&&t.map?(r=r||new utils.SourceMapConsumer(t.map),s.liveCommentLines=_.chain(s.liveCommentLines).map((e,t)=>({line:parseInt(t,10),column:e+1})).map(({line:e,column:t})=>utils.mapTransformedRangeToOriginal(r,[e,t,e,t])).filter(e=>e&&e.length).map(e=>e[0]).value()):s.liveCommentLines&&(s.liveCommentLines=Object.keys(s.liveCommentLines).map(e=>parseInt(e,10)).filter(e=>e).map(e=>e));o={id:viteNodeContext.file.id,transformed:_.omit(t,"code"),instrumented:_.omit(s,"map"),ts:viteNodeContext.file.ts,originalTs:viteNodeContext.file.originalTs,runnerCacheKey:viteNodeContext.file.runnerCacheKey,transformedTime:(new Date).toISOString()};if(n)throw tracer.sendTransformedFile(o),n;return viteNodeContext.instrumentedFiles[e]=o,{code:s.code,map:s.map}}});let i=1,c={};function s(r,o){return new Promise((e,t)=>{c[i]={resolve:e,reject:t},n.send({type:r,id:i,payload:o}),i++})}const a={reset:async e=>{i=1,c={},await s("reset",e)},resolveId:async e=>s("resolveId",e),fetchModule:async e=>s("fetchModule",e)};n.once("message",async e=>{var t;n.on("message",async t=>{if(t)if(t.returnId){var{returnId:e,result:r,error:o}=t;if(c[e]){if(o){const s=JSON.parse(o),a=new Error;Object.keys(s).forEach(e=>{a[e]=s[e]}),c[e].reject(a)}else c[e].resolve(r);delete c[e]}}else if(t&&t.type&&l[t.type])try{var i=await l[t.type](t.payload);t.id&&n.send({returnId:t.id,result:i})}catch(e){t.id&&(o=Object.assign(Object.assign({},e),{message:e.message,stack:e.stack}),n.send({returnId:t.id,error:JSON.stringify(o)}))}}),"ready"===e.type?(t=await s("start",{localProjectRoot:localProjectRoot,vitePath:quokkaSettings.vite.vitePath,viteNodeServerPath:quokkaSettings.vite.viteNodeServerPath,quokkaSettingsDirPath:path.dirname(quokkaSettings.globalConfigFile),quokkaTempDirPath:quokkaSettings.tempDir,fileType:quokkaSettings.vite.fileType}),a.config=t,r(a)):o(e.error||"Unknown error starting vite node"),n.on("error",e=>{o(e)})})})}utils.patchModulesCode([{files:["scheduler/cjs/scheduler.development.js"],replacements:[{from:"typeof window === 'undefined'",to:"true || typeof window === 'undefined'",optional:!0}]},{files:["tsconfig-paths/lib/register.js"],replacements:[{from:"if (!isCoreModule) {",to:"if (!isCoreModule && (!_parent || !require('path').relative(configLoaderResult.absoluteBaseUrl, _parent.filename).startsWith('..'))) {"},{from:"console.warn",to:"",optional:!0}]},{files:["@babel/plugin-proposal-private-property-in-object/lib/index.js"],replacements:[{from:"setTimeout(console.warn, 2500",to:"setTimeout(() => {}, 0",optional:!0}]}]),patchConsoleError(),tracer._maxLogEntrySize=1048576,tracer._hiddenGlobalProps={$_$baseDir:1,$_$slow:1,$_$testFiles:1,$_$tests:1,$_$session:1,$_$initialSpecId:1,$_$coverage:1},process.on("unhandledRejection",function(e){throw e});try{quokkaSettings=JSON.parse(process.env.quokka),quokkaSettingsDirPath=path.dirname(quokkaSettings.globalConfigFile);const quokkaTempDirPath=quokkaSettings.tempDir;quokkaSettings.nativeEsm&&(tracer._esm={localProjectDirUrl:url.pathToFileURL(localProjectRoot).href,settingsDirUrl:url.pathToFileURL(quokkaSettingsDirPath).href,tempDirUrl:url.pathToFileURL(quokkaTempDirPath).href}),quokkaSettingsDirNodeModulesPath=path.join(quokkaSettingsDirPath,"node_modules"),quokkaTempDirNodeModulesPath=path.join(quokkaTempDirPath,"node_modules")}catch(e){}var requireFromTheProjectAndGlobalSettingsContext=function(e){var t=Module._load(e,entryModule,!1);try{var r=Module._resolveFilename(e,entryModule,!1);tracer._doWhenReceiverIsReady(function(){tracer._send("module",{path:r})})}catch(e){}return t},rootEntryModule=(requireFromTheProjectAndGlobalSettingsContext.extensions=require.extensions,requireFromTheProjectAndGlobalSettingsContext.resolve=require.resolve,new Module(".",null)),requireForPlugin=(rootEntryModule.filename=path.join(localProjectRoot,"index.js"),rootEntryModule.path=localProjectRoot,rootEntryModule.paths=Module._nodeModulePaths(localProjectRoot).concat([quokkaSettingsDirNodeModulesPath,quokkaTempDirNodeModulesPath]),function(t){if(!t||"."!==t[0])return Module._load(t,rootEntryModule,!1);try{return Module._load(path.resolve(quokkaSettingsDirPath,t),rootEntryModule,!1)}catch(e){return Module._load(t,rootEntryModule,!1)}}),hideProp=(requireForPlugin.extensions=require.extensions,requireForPlugin.resolve=require.resolve,function(e){Object.defineProperty(global,e,{enumerable:!1,value:global[e]})});Object.keys(global).filter(function(e){return"wallaby"===e||0===e.indexOf("$_$")}).forEach(function(e){hideProp(e)}),require.extensions[".jsx"]=require.extensions[".js"];const registerAssetExtensions=()=>{[".png",".svg",".ico",".jpeg",".jpg",".css",".less",".scss",".sass",".htm",".html"].forEach(function(e){require.extensions[e]=function(){}})};tracer._identifierExpressionAutoLogHitLimit=10,tracer._logLimit=Math.max(quokkaSettings.logLimit||100,10);var toInitialize={vite:!0,babel:!0,ts:!0,js:!0,plugins:!0,globals:["assert","events","fs","os","path"]},runBeforeEach=[],starter={quokkaStackTraceMarker:async function(){var sessionId=global.$_$session;if(global.$_$resolveGetters=quokkaSettings.resolveGetters,isBun){function escapeRegex(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}Bun.plugin({name:"quokka",setup(e){e.module("quokkaBunModuleTraceMarker",()=>({contents:file.content,loader:"js"})),e.onLoad({filter:new RegExp(escapeRegex(file.path),"i"),namespace:"file"},async e=>e.path===file.path?{contents:file.content,loader:"js"}:{contents:fs.readFileSync(e.path),loader:path.extname(e.path).slice(1)})}})}else{if(quokkaSettings.vite)toInitialize.vite&&(global._quokkaLazyLoadHelperFunctions(),viteNodeContext.serverPromise=createViteNodeServer(),toInitialize.vite=!1);else{if(quokkaSettings.babel&&toInitialize.babel){var babelConfig={ignore:"string"==typeof quokkaSettings.babel.ignore?new RegExp(quokkaSettings.babel.ignore):"[object Array]"===Object.prototype.toString.call(quokkaSettings.babel.ignore)?quokkaSettings.babel.ignore:function(e){return~e.indexOf("quokka.js")||~e.indexOf("node_modules")},presets:quokkaSettings.babel.presets,plugins:quokkaSettings.babel.plugins,extensions:[".js",".jsx",".es6",".es",".mjs",".ts",".tsx",".cjs",".mjs",".cts",".mts"]},babelMajorVersion=NaN;if(quokkaSettings.babel.version&&(babelMajorVersion=parseInt(quokkaSettings.babel.version.split(".")[0],10)),7<=babelMajorVersion){utils.patchBabelResolve(quokkaSettings.babel.path);try{"[object Array]"!==Object.prototype.toString.call(babelConfig.ignore)&&(babelConfig.ignore=[babelConfig.ignore]),require(path.join(path.dirname(quokkaSettings.babel.path),"register"))(babelConfig)}catch(e){try{utils.patchModule("@babel/core",()=>require(quokkaSettings.babel.path)),require(quokkaSettings.babel.registerPath)(babelConfig)}catch(e){console.warn("@babel/register could not be launched properly. This may indicate that your project packages are not compatible with your current version of Quokka.\nPlease install @babel/register as a project dependency.\nYou may install the module in your project by running `npm install @babel/register` command.")}}}else require(path.join(quokkaSettings.babel.path,"register"))(babelConfig);if(quokkaSettings.babel.polyfill&&(7<=babelMajorVersion?require(path.join(path.dirname(quokkaSettings.babel.path),"polyfill")):require(path.join(path.dirname(quokkaSettings.babel.path),"babel-polyfill"))),quokkaSettings.babel.tsconfigPaths)try{require(path.join(quokkaSettings.babel.tsconfigPaths.path,"register"))}catch(e){}delete toInitialize.babel}if(quokkaSettings.ts&&toInitialize.ts&&(initializeTs(),delete toInitialize.ts),quokkaSettings.js&&toInitialize.js&&quokkaSettings.js.compilerOptions&&quokkaSettings.js.compilerOptions.baseUrl&&quokkaSettings.js.compilerOptions.paths){try{path.isAbsolute(quokkaSettings.js.compilerOptions.baseUrl)?require(path.join(quokkaSettings.js.tsconfigPaths.path,"lib","register")).register({baseUrl:quokkaSettings.js.compilerOptions.baseUrl,paths:quokkaSettings.js.compilerOptions.paths}):require(path.join(quokkaSettings.js.tsconfigPaths.path,"lib","register")).register({baseUrl:quokkaSettings.js.compilerOptions.pathsBasePath||quokkaSettings.js.compilerOptions.baseUrl,paths:quokkaSettings.js.compilerOptions.paths})}catch(e){}delete toInitialize.js}}registerAssetExtensions()}if(quokkaSettings.plugins&&toInitialize.plugins){if(quokkaSettings.builtInPlugins&&quokkaSettings.builtInPlugins.find(e=>"auto-detect:create-react-app"===e))try{global.React=requireForPlugin("react")}catch(e){}"string"==typeof quokkaSettings.plugins&&(quokkaSettings.plugins=[quokkaSettings.plugins]),quokkaSettings.plugins.slice().forEach(function(e){var t;"jsdom-quokka-plugin"===e?((t=require("./jsdomQuokkaPlugin")).before&&t.before(requireForPlugin,quokkaSettings),t.beforeEach&&runBeforeEach.push(t.beforeEach)):((t=requireForPlugin(e)).before&&t.before(quokkaSettings),t.beforeEach&&runBeforeEach.push(t.beforeEach))}),delete toInitialize.plugins}toInitialize.globals&&(toInitialize.globals.forEach(function(e){global[e]||(global[e]=require(e))}),delete toInitialize.globals),runBeforeEach.forEach(function(e){e(quokkaSettings)});var beforeExitHandler=function(){var e;tracer._asyncCodeMayBeExecuting=!1,tracer.ref(),sessionId!==global.$_$session?delete tracer._pong:(startTime&&(e=(1e3*(e=process.hrtime(startTime))[0]+e[1]/1e6).toFixed(2)),tracer._pong&&(tracer._pong(),delete tracer._pong),isBun&&Object.keys(require.cache).forEach(e=>{e.startsWith(localProjectRoot)&&tracer._doWhenReceiverIsReady(function(){tracer._send("module",{path:e})})}),tracer.complete({time:e}))},runner=(process.once("beforeExit",beforeExitHandler),beforeExitHandlers.push(beforeExitHandler),{quokkaStackTraceMarker:async function(){if(startTime=process.hrtime(),quokkaSettings&&quokkaSettings.vite&&!isBun)return runInViteNode(await viteNodeContext.serverPromise);if(quokkaSettings&&quokkaSettings.nativeEsm&&!isBun){const fileUrl=url.pathToFileURL(file.path.replace(/$quokka.js^/,"quokka.mjs")),nodeVersion=(fileUrl.href=fileUrl.href+"?session="+sessionId,tracer._esm.scratchFileUrl=fileUrl.href,tracer._esm.scratchFileContent=file.content,semver.clean(process.version));if(semver.gte(nodeVersion,"19.0.0")&&semver.lt(nodeVersion,"20.0.0")||semver.lt(nodeVersion,"20.0.0")&&semver.lt(nodeVersion,"18.19.0")||semver.gte(nodeVersion,"20.0.0")&&semver.lt(nodeVersion,"20.6.0"))global.$_$esmHooksPort&&(global.$_$esmHooksPort.postMessage({quokkaSettings:quokkaSettings,serverPath:serverPath,scratchFileUrl:fileUrl.href,scratchFileContent:file.content,localProjectDirUrl:tracer._esm.localProjectDirUrl,settingsDirUrl:tracer._esm.settingsDirUrl,tempDirUrl:tracer._esm.tempDirUrl,sessionId:sessionId}),global.$_$esmHooksPort.onmessage=e=>{e&&e.data&&"tracer._doWhenReceiverIsReady._send"===e.data.method&&tracer._doWhenReceiverIsReady(()=>{tracer._send.apply(tracer,e.data.args)})});else if(registered)registerReadyPromise=new Promise(e=>{registerReady=e}),global.$_$clientPort.postMessage({quokkaSettings:quokkaSettings,serverPath:serverPath,scratchFileUrl:fileUrl.href,scratchFileContent:file.content,localProjectDirUrl:tracer._esm.localProjectDirUrl,settingsDirUrl:tracer._esm.settingsDirUrl,tempDirUrl:tracer._esm.tempDirUrl,sessionId:sessionId}),await registerReadyPromise;else{registered=!0;const{register}=require("node:module"),{pathToFileURL}=require("node:url"),{MessageChannel}=require("node:worker_threads"),{port1:clientPort,port2:esmPort}=new MessageChannel;global.$_$clientPort&&global.$_$clientPort.close(),global.$_$esmPort&&global.$_$esmPort.close(),clientPort.addEventListener("message",e=>{e&&e.data&&"tracer._doWhenReceiverIsReady._send"===e.data.method?tracer._doWhenReceiverIsReady(()=>{tracer._send.apply(tracer,e.data.args)}):e&&e.data&&"ready"===e.data.method&&registerReady()}),global.$_$clientPort=clientPort,global.$_$esmPort=esmPort,clientPort.unref(),esmPort.unref(),await register("./hooks-v2.mjs",{parentURL:pathToFileURL(__filename).href,data:{quokkaSettings:quokkaSettings,serverPath:serverPath,scratchFileUrl:fileUrl.href,scratchFileContent:file.content,localProjectDirUrl:tracer._esm.localProjectDirUrl,settingsDirUrl:tracer._esm.settingsDirUrl,tempDirUrl:tracer._esm.tempDirUrl,sessionId:sessionId,port:esmPort},transferList:[esmPort]})}await eval(_831‍.c("import(fileUrl)"))}else if(isBun&&file.esm){const fileUrl=url.pathToFileURL(file.path);quokkaFileExists?entryModule._compile(`(async () => {
              try {
                await import('${fileUrl}');
              } catch (e) {
                global.$_$tracer?._reportError(e);
              }
            })()`,"quokka_bun_import_root.js"):entryModule._compile(`(async () => {
              try {
                await import('quokkaBunModuleTraceMarker');
              } catch (e) {
                global.$_$tracer?._reportError(e);
              }
            })()`,"quokka_bun_import_root.js")}else entryModule._compile(file.content,file.path)}});try{await runner.quokkaStackTraceMarker()}catch(e){if(sessionId===global.$_$session){if(!process.versions.bun)throw e;tracer._reportError(e)}}finally{tracer._asyncCodeMayBeExecuting=!0,tracer.unref(),global.$_$esmHooksPort&&global.$_$esmHooksPort.unref&&global.$_$esmHooksPort.unref()}}};function initializeTs(){switch(quokkaSettings.ts.compilerName){case"tsx":initializeTsx(quokkaSettings.ts.compilerPath);break;case"@swc-node/register":initializeSwc(quokkaSettings.ts.compilerPath);break;case"ts-node":initializeTsnode()}initializeTsConfig()}function initializeTsConfig(){try{if(quokkaSettings.ts.tsconfigPaths){if(quokkaSettings.nativeEsm)try{var e,t,r=require(quokkaSettings.ts.tsconfigPaths.path);quokkaSettings.ts.compilerOptions&&quokkaSettings.ts.compilerOptions.baseUrl&&quokkaSettings.ts.compilerOptions.paths?tracer._esm.tsConfigPathsMatchPath=r.createMatchPath(quokkaSettings.ts.compilerOptions.baseUrl,quokkaSettings.ts.compilerOptions.paths):({absoluteBaseUrl:e,paths:t}=r.loadConfig(),tracer._esm.tsConfigPathsMatchPath=r.createMatchPath(e,t))}catch(e){}quokkaSettings.ts.compilerOptions&&quokkaSettings.ts.compilerOptions.baseUrl&&quokkaSettings.ts.compilerOptions.paths?require(path.join(quokkaSettings.ts.tsconfigPaths.path,"lib","register")).register({baseUrl:quokkaSettings.ts.compilerOptions.baseUrl,paths:quokkaSettings.ts.compilerOptions.paths}):require(path.join(quokkaSettings.ts.tsconfigPaths.path,"register"))}}catch(e){}}function initializeTsnode(){var e,t,r,o=quokkaSettings.ts.tsNode.ignore||["(?:^|/)node_modules/"],o=((o=Array.isArray(o)?o:[o]).push(serverPath),{compiler:process.env.TS_NODE_COMPILER,ignore:o,ignoreDiagnostics:(process.env.TS_NODE_IGNORE_DIAGNOSTICS||"").split(",").map(e=>parseInt(e,10)).filter(e=>e),compilerOptions:{experimentalDecorators:!(null==(o=null==(o=quokkaSettings.ts)?void 0:o.compilerOptions)||!o.experimentalDecorators)}}),i=(quokkaSettings.nativeEsm&&(o.experimentalEsmLoader=!0,o.transpileOnly=!0),quokkaSettings.ts.swc&&(o.swc=!1,o.transpileOnly=!0,o.transpiler=[quokkaSettings.ts.swcTranspilerPath,{swc:quokkaSettings.ts.swcPath}]),path.join(quokkaSettings.ts.tsNode.path)),s=require(i),o=s.register(o);quokkaSettings.nativeEsm&&(tracer._esm.tsNode=o,semver.gte(s.VERSION,"10.8.0")?(e=path.join(i,"/dist-raw/node-internal-modules-esm-resolve"),t=require(e),r=require(path.join(i,"/dist/file-extensions")).getExtensions(o.config,o.options,o.ts.version),tracer._esm.tsNodeResolve=t.createResolve({extensions:r,preferTsExts:o.options.preferTsExts,tsNodeExperimentalSpecifierResolution:o.options.experimentalSpecifierResolution})):(e=path.join(i,"/dist-raw/node-esm-resolve-implementation"),t=require(e),(r=s.getExtensions(o.config)).preferTsExts=o.options.preferTsExts,tracer._esm.tsNodeResolve=t.createResolve(r)))}function initializeTsx(e){process.env.ESBUILD_WORKER_THREADS="0",require(path.join(e,"/dist/cjs/api/index.cjs")).register()}function initializeSwc(e){require(path.join(e,"index.js"))}tracer.start(starter.quokkaStackTraceMarker),module.exports={init:function(e){var t=global.$_$testFiles[0];return isBun&&(t.type!==t.originalType&&(t.path=t.path.substring(0,t.path.length-t.type.length)+t.originalType,e[0]=t.path),quokkaFileExists=fs.existsSync(t.path)),file={path:e[0],content:t.content,esm:t.esm},(entryModule=new Module(".",null)).filename=file.path,entryModule.path=path.dirname(file.path),entryModule.paths=Module._nodeModulePaths(path.dirname(entryModule.filename)).concat([quokkaTempDirNodeModulesPath,quokkaSettingsDirNodeModulesPath]),entryModule.require=requireFromTheProjectAndGlobalSettingsContext,quokkaSettings&&(quokkaSettings.filePath=file.path,quokkaSettings.vite)&&(viteNodeContext.file=t,viteNodeContext.instrumentedFiles={}),beforeExitHandlers.forEach(function(e){process.removeListener("beforeExit",e)}),beforeExitHandlers.length=0,Object.keys(tracer._hiddenGlobalProps).forEach(function(e){hideProp(e)}),{}}};