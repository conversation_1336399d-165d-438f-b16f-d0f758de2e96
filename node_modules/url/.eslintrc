{
	"root": true,

	"extends": "@ljharb",

	"rules": {
		"complexity": 0,
		"eqeqeq": [2, "allow-null"],
		"func-style": 0,
		"id-length": 0,
		"indent": [2, 2],
		"max-depth": 0,
		"max-lines": 0,
		"max-lines-per-function": 0,
		"max-statements": 0,
		"max-statements-per-line": [2, { "max": 3 }],
		"no-continue": 1,
		"no-empty": 1,
		"no-magic-numbers": 0,
		"no-mixed-operators": 1,
		"max-nested-callbacks": 0,
		"no-param-reassign": 1,
		"no-plusplus": 0,
		"no-redeclare": 1,
		"no-restricted-syntax": 1,
		"no-script-url": 1,
		"sort-keys": 0,
	},

	"overrides": [
		{
			"files": "test/**/*",
			"rules": {
				"no-sequences": 1,
			},
		},
	],
}
