import { QueryParser } from "../src/core/queryParser";

const query1 =
  "/Orders?$select=OrderId,OrderDate,Status&$expand=Customer($select=CustomerName,Email),OrderItems($select=ProductName,Quantity,UnitPrice)&$filter=Status ne 'Cancelled'";
const query2 =
  "/Products?$select=ProductId,Name,Price&$expand=Category($select=CategoryName,Description;$filter=IsActive eq true)&$filter=Price lt 1000";
const query3 =
  "/Customers?$expand=Orders($expand=OrderItems($expand=Product($select=Name,Category);$select=Quantity,UnitPrice);$select=OrderDate,Status)&$top=10";
const query4 =
  "/Employees?$filter=Age gt 25 and Salary le 75000.50 and IsManager eq false and Department in ('Sales','Marketing','Support') and HireDate gt datetime'2020-01-01T00:00:00Z'";
const query5 =
  "/Categories?$expand=Products($expand=OrderItems($expand=Order($select=OrderDate,CustomerName;$filter=Status eq 'Completed');$select=Quantity;$filter=Quantity gt 1);$select=Name,Price;$filter=Price gt 10;$orderby=Price desc)&$orderby=CategoryName";
const query6 =
  "/Products?$filter=((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0)&$select=Name,Category,Price,Rating";
const query7 =
  "/Products?$filter=((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0)&$select=Name,Category,Price,Rating";
const query8 =
  "/Customers?$filter=contains(tolower(CompanyName),'tech') and length(ContactName) gt 5 and indexof(Email,'@gmail.com') ne -1&$orderby=CompanyName,ContactName";
const query9 =
  "/Orders?$expand=Customer($select=CustomerName,Country;$filter=Country eq 'USA'),OrderItems($expand=Product($select=Name,Category;$filter=Category ne 'Discontinued');$filter=Quantity gt 2 and UnitPrice lt 100)&$filter=OrderDate gt datetime'2023-06-01T00:00:00Z'";
const query10 = `/Products?$filter=Description eq 'High-quality "premium" product & accessories' and Name contains 'O''Reilly'`;
const query11 =
  "/Employees?$filter=Manager eq null and IsActive eq true and Salary ne null&$select=EmployeeId,Name,Department";
const query12 =
  "/Companies?$expand=Departments($expand=Employees($expand=Projects($expand=Tasks($select=TaskName,Status;$filter=Status eq 'InProgress');$select=ProjectName,StartDate);$select=FirstName,LastName,Position;$filter=IsActive eq true);$select=DepartmentName;$filter=Budget gt 100000)&$top=5";
const query13 =
  "/OrderItems?$filter=(Quantity * UnitPrice) gt 500 and (Quantity * UnitPrice * 0.1) le 100&$select=ProductName,Quantity,UnitPrice&$orderby=(Quantity * UnitPrice) desc";
const query14 = "";

const parser = new QueryParser(query5);

// Get individual components
const selectFields = parser.getSelect();
const whereClause = parser.getWhere();
const joins = parser.getJoins();
const orderBy = parser.getOrderBy();

// Get complete parsed query object
// const fullQuery = parser.getParsedQuery();

// Generate SQL-like string
const sqlString = parser.toSQL();

console.log("--------->", {
  selectFields,
  whereClause,
  joins,
  orderBy,
  sqlString,
});
