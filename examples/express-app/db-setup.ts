import { DataSource, ODataControler, ExpressRouter } from "../../src";
import { Express } from "express";

const createSchema = (app: Express) => {
  const dataSource = new DataSource({
    dialect: "postgres",
    database: "twostapp",
    username: "sysadmin",
    password: "gFp0sd=wjJbB2y=D=Xq0FkvSSvgSOS",
    host: "twostapp-prod.chk68km08hmu.ca-central-1.rds.amazonaws.com",
    port: 5432,
    pool: {},
    schema: "public",
    ssl: true,
  });
  const User = dataSource.defineEntity("user", {
    id: {
      dataType: "int",
      isPrimaryKey: true,
      isNullable: false,
    },
    user_key: {
      dataType: "uuid",
      isNullable: false,
    },
    email: {
      dataType: "email",
      isNullable: false,
      isUnique: true,
    },
    first_name: {
      dataType: "string",
      isNullable: false,
    },
    last_name: {
      dataType: "string",
      isNullable: false,
    },
    gender: {
      dataType: "string",
      isNullable: true,
    },
    created_at: {
      dataType: "date",
      isNullable: false,
    },
    updated_at: {
      dataType: "date",
      isNullable: false,
    },
    contacts: {
      dataType: "int-test",
      isNullable: true,
    },
    sdsd: {
      reference:''

    },
  });

  class UserController extends ODataControler {
    constructor() {
      super({
        model: User,
        allowedMethod: ["get"],
        endpoint: "user",
      });
    }
    get(query: object) {
      console.log("------->hgey called");
      return query;
    }
  }

  const userController = new UserController();
  userController.getAllowedMethod();

  const config = {
    controllers: [userController],
    dataSource,
  };

  new ExpressRouter(app, config);
};

export { createSchema };
